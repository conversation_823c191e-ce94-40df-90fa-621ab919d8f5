(function ($) {

  // Terminology
  // ControlWindow  - This is the main chat window that contains the filter and user groups/users
  // ChatMessageWindow - This is the popup message window the users use to communicate with each other

  var g_clientsPendingInvite = []; // an array of user ids to invite to a group chat
  var _isInviteMode = false; // when invite mode is true, no other chats are allowed to be opened, do not set directly use setInviteMode
  var g_inviteToGroupId = ''; // this is the groupid that the users are being invited to, do not set directly use setInviteToGroupId
  var g_groupHostNameId = ''; // this is the host name id of the group, do not set directly use setInviteToGroupId

  function setInviteMode(mode, groupId, hostNameId) {
    _isInviteMode = mode;
    g_clientsPendingInvite = []; /// clear any pending invites
    g_inviteToGroupId = groupId;
    if (hostNameId !== '') {
      g_groupHostNameId = hostNameId;
    }
    if (!mode) {
      $('.invited').removeClass('invited');
      // Show all group chats and New Chat Group link when exiting invite mode
      $('.category.group').show();
      $('.new-group-container').show();
    } else {
      // Hide ALL group chats and New Chat Group link when entering invite mode
      $('.category.group').hide();
      $('.new-group-container').hide();
    }
  }

  function clearInvitedUsers() {
    setInviteMode(false, '', '');
  }

  // Helper function to check if current user is alone in a group
  function isUserAloneInGroup(group) {
    var activeUsers = group.users.filter(function(user) {
      return !user.leftGroup && user.userNameId !== settings.userNameId;
    });
    return activeUsers.length === 0;
  }

  $.fn.cleoChat = function (options) {
    var settings = $.extend({
      userName: "",
      userRole: "Other",
      userLocation: "Other",
      userBase: "Other",
      userNameId: "",
      hubServer: "",
      tenantId: "",
      jwtToken: "F104DF7789CEA414E9A3FC2E535F02F567508990DBA3F3828757AFC2D3977F35"
    }, options);

    if (settings.userRole === "" || settings.userRole === null) settings.userRole = "Other";
    if (settings.userBase === "" || settings.userBase === null) settings.userBase = "Other";
    if (settings.userLocation === "" || settings.userLocation === null) settings.userLocation = "Other";
    if (settings.tenantId === "" || settings.tenantId === null) settings.tenantId = "Other";

    var state = "closed";

    if (settings.userNameId === "") return null;

    var usersWindowContainer = $("<div/>")
      .attr("id", "usersWindowContainer")
      .appendTo(this);

    var usersWindow = $("<div/>")
      .attr("id", "usersWindow")
      .appendTo(usersWindowContainer);

    var usersWindowTitle = $("<div/>")
      .attr("id", "usersWindowTitle")
      .attr("class", settings.tenantId)
      .html("<p>Cleo Chat</p>")
      .append($("<img/>"))
      .appendTo(usersWindow);

    var minimizeWindowContainer = $("<div/>")
      .attr("id", "minimizeWindowContainer")
      .click(function () {
        usersWindow.slideUp();
      })
      .appendTo(usersWindowTitle);

    var minimizeWindow = $("<div/>")
      .attr("id", "minimizeWindow")
      .appendTo(minimizeWindowContainer);

    var currentUser = $("<div/>")
      .attr("id", "currentUser")
      .appendTo(usersWindow);

    var imgAvatar = $("<img/>")
      .attr("id", "imgAvatar")
      .attr("alt", "")
      .appendTo(currentUser);

    // Check if avatar upload feature is enabled before setting avatar source
    checkAvatarUploadFeature().then(function(isEnabled) {
      if (isEnabled) {
        // Set dynamic avatar image source
        imgAvatar.attr("src", settings.hubServer + "avatar/" + settings.tenantId + "/" + settings.userNameId);
        
        // Add pointer cursor and double-click handler for avatar upload
        imgAvatar.css("cursor", "pointer");
        imgAvatar.dblclick(function() {
          showAvatarUploadPopup();
        });
      } else {
        // Use default avatar when feature is disabled
        imgAvatar.attr("src", "css/images/avatar.jpg");
      }
    });

    var userDetails = $("<div/>")
      .attr("id", "userDetails")
      .html("<p>Unknown</p>")
      .appendTo(currentUser);

    var availability = $("<select/>")
      .attr("id", "availability")
      .change(function () {
        var status = parseInt($(this).val());
        safeInvoke("ChangeStatus", settings.userNameId, settings.tenantId, status);
      })
      .html("<option value='0'>Available</option><option value='1'>Do not disturb</option><option value='2'>Away</option>")
      .appendTo(userDetails);

    var divider = $("<div/>").html("&nbsp;").appendTo(currentUser).css("divider");

    var groups = $("<div/>").attr("id", "groups").appendTo(usersWindow);

    var usersWindowHeader = $("<div/>").attr("id", "usersWindowHeader").html("<p>No Contacts Online</p>").click(function () { usersWindow.slideDown(); }).appendTo(usersWindowContainer);

    var windowSpacer = $("<div/>").attr("id", "windowSpacer").html("<p>Offline</p>").appendTo(usersWindowContainer);

    // Create version popup
    var versionPopup = $("<div/>").attr("id", "versionPopup").html("Loading...").appendTo($("body"));

    var usersMessagesContainer = $("<div/>").attr("id", "usersMessagesContainer").appendTo(this);

    // Event handlers
    $("body").on("click", ".categoryheader", function () {
      toggleCategory(this);
    });

    $("body").on("click", ".closeOpen, .close", function () {

      closeMessageWindow(this);
      clearInvitedUsers();
      $('.new-group-container').show();

    });

    $("body").on("click", ".decline", function () {
      var groupId = $(this).data('id');
      closeMessageWindow(this);
      safeInvoke("DeclineGroupInvite", groupId, settings.userName);
    });

    $("body").on("click", ".accept", function () {
      var groupId = $(this).data('id');
      console.log('=== INVITE ACCEPTANCE CLICKED ===');
      console.log('Group ID:', groupId);
      console.log('Current user:', settings.userName);
      console.log('User ID:', settings.userNameId);
      console.log('Tenant ID:', settings.tenantId);
      
      closeMessageWindow(this);
      clearInvitedUsers();
      
      console.log('Calling AcceptGroupInvite with:', groupId, settings.userName);
      console.log('Connection state:', connection ? connection.state : 'No connection');
      console.log('Connection active:', isActive());
      
      safeInvoke("AcceptGroupInvite", groupId, settings.userName)
        .then((result) => {
          console.log('AcceptGroupInvite SUCCESS:', result);
        })
        .catch((error) => {
          console.error('AcceptGroupInvite ERROR:', error);
        });
      
      console.log('AcceptGroupInvite call initiated');
    });

    $("body").on("keydown", ".chat-input", function (e) {
      var groupId = $(this).attr("data-id"); // Using attr to get the updated value
      if (groupId == undefined) return;
      var thisChatInput = $('.chat-input[data-id="' + groupId + '"]');
      if (e.keyCode === 13) {
        var isGroupChat = $(thisChatInput).data("isgroupchat");
        var startMessageDateTime = new Date($.now());
        var messageTo = $(thisChatInput).closest(".messageWindow").attr("id").replace("group-", "");
        var message = capitaliseWords($(thisChatInput).val());
        if (!isGroupChat) {
          message = $(thisChatInput).val();
          var conversationContainer = $(thisChatInput).closest(".messageWindow").find(".conversationContainer").eq(0);
          drawMessage(conversationContainer, message, "out", settings.userName, "");
          safeInvoke("SendMessageToUser", settings.userNameId, messageTo, message, startMessageDateTime, settings.tenantId);
        } else {
          // if groupid is NewChatWindow then register the group as it has not yet been registered.
          // once registered the group id will be updated to the assigned group id
          if (groupId === "NewChatWindow") {
            // register group                        
            safeInvoke("RegisterGroup", message);
          } else {
            safeInvoke("SendGroupMessageToUsers", settings.userNameId, groupId, message, startMessageDateTime, settings.tenantId);
          }
        }
        $(thisChatInput).val("");
      }
    });

    $("body").on("click", ".remove-link", function (e) {
      var userNameId = $(this).data('id');
      var groupId = $(this).data('groupid');
      safeInvoke("LeaveGroup", groupId, userNameId);
    });

    $("body").on("click", ".btn-confirm-delete-group", function (e) {
      var groupId = $(this).data('id');
      safeInvoke("DeleteGroup", settings.tenantId, groupId);
      var contactContainer = $('#group-' + groupId).find(".contactContainer");
      $(contactContainer).find('.invite-container').show();
    });

    $("body").on("click", ".btn-discard-delete-group", function (e) {
      var groupId = $(this).data('id');
      $('#delete_box_' + groupId).parent().parent().remove();
      var contactContainer = $('#group-' + groupId).find(".contactContainer");
      $(contactContainer).find('.invite-container').show();
      var messageContentContainer = $(contactContainer).parent();
      $(messageContentContainer).find('.inputContainer').show();
      $(messageContentContainer).removeClass('no-chat-bar');
    });

    $("body").on("click", ".btn-discard-leave-group", function (e) {
      var groupId = $(this).data('id');
      $('#leave_box_' + groupId).parent().parent().remove();
      var contactContainer = $('#group-' + groupId).find(".contactContainer");
      $(contactContainer).find('.invite-container').show();
      var messageContentContainer = $(contactContainer).parent();
      $(messageContentContainer).find('.inputContainer').show();
      $(messageContentContainer).removeClass('no-chat-bar');

    });

    $("body").on("click", ".btn-confirm-leave-group", function (e) {
      var groupId = $(this).data('id');
      $('#leave_box_' + groupId).parent().parent().remove();
      var contactContainer = $('#group-' + groupId).find(".contactContainer");
      $(contactContainer).find('.invite-container').show();
      var contactContainer = $('#group-' + groupId).find(".contactContainer");
      closeMessageWindow(contactContainer);
      safeInvoke("LeaveGroup", groupId, settings.userNameId);
    });

    // Double-click event handler for version popup
    $("body").on("dblclick", "#windowSpacer", function (e) {
      e.preventDefault();
      e.stopPropagation();
      
      var popup = $("#versionPopup");
      var spacer = $("#windowSpacer");
      
      // Get version from features API if not already loaded
      if (popup.html() === "Loading...") {
        getFeatures().then(function(features) {
          var version = features.Version || features.version || "Not set";
          popup.html(version);
          showVersionPopup(popup, spacer);
        });
      } else {
        showVersionPopup(popup, spacer);
      }
    });
    
    function showVersionPopup(popup, spacer) {
      
      // Get position of windowSpacer element
      var spacerOffset = spacer.offset();
      var spacerWidth = spacer.outerWidth();
      var spacerHeight = spacer.outerHeight();
      
      // Position popup above the windowSpacer
      popup.css({
        left: spacerOffset.left + (spacerWidth / 2) - (popup.outerWidth() / 2),
        top: spacerOffset.top - popup.outerHeight() - 10
      });
      
      // Show popup
      popup.fadeIn(200);
      
      // Hide popup after 2 seconds
      setTimeout(function() {
        popup.fadeOut(200);
      }, 2000);
    }
    
    // End of showVersionPopup function

    // Hide version popup when clicking elsewhere
    $("body").on("click", function (e) {
      if (!$(e.target).closest("#windowSpacer, #versionPopup").length) {
        $("#versionPopup").fadeOut(200);
      }
    });

    // Event handlers for elements that are dynamically created
    // On keyup for the filter input, filter the users in the Cleo Chat Window
    $(document).on('keyup', 'input.filter', function (e) {
      filterControlWindowUsers(e.target.value);
    });

    // On Click event for new-group-container, this will draw the new chat window
    $(document).on('click', '.new-group-container', function (e) {
      // generate an initial guid for the group chat, this will be replaced with another assigned guid if the group chat is successfully created
      //var guid = generateGUID();
      $('.new-group-container').hide();
      drawChatMessageWindow("NewChatWindow", "Assistant", "Please give your chat group a name in the box below and press enter.", "out", "Assistant", "New Group Chat", true, true, "", true);
      // set the max length of the chat input to 16 characters     
      $('.chat-input[data-id="NewChatWindow"]').attr('maxLength', 18);
    });

    $(document).on('click', '.invite-users', function (e) {
      var groupId = $(this).data('id');
      if (g_inviteToGroupId !== '') {
        // if the user is already in invite mode then cancel the invite mode
        drawChatMessageWindow(groupId, "Assistant", "You are currently selecting users to invite in another chat window. Send invites or select cancel from the other chat window first.", "out", "Assistant", "New Group Chat", true, true, "", true);
        return;
      }
      
      // Expand the main panel if it's minimized so users can see and select invitees
      var usersWindow = $('#usersWindow');
      if (!usersWindow.is(':visible')) {
        console.log('Expanding main panel for invite mode');
        usersWindow.slideDown();
      }
      
      // Get the host name ID for this specific group
      var groupHostNameId = $(this).closest('.category').data('hostnameid');
      setInviteMode(true, groupId, groupHostNameId);
      drawChatMessageWindowMenuBar(groupId, groupHostNameId);
      if (_isInviteMode) {
        $('.category').not('.group').each(function () {
          var currentId = $(this).attr('id'); // Get the current element's ID
          $(this).prepend("<div class='select-all-container' data-id='" + currentId + "'><a href='#'>Select all</a></div>");
        });
        // find all the elements with the category class but not the group class
        $('.category ').not('.group').prepend("<div class='select-all-container'><a href='#'>Select all</a></div>");
      }
    });

    $(document).on('click', '.leave-group', function (e) {
      var groupId = $(this).data('id');
      onLeaveGroupRequest(groupId);
    });

    // delete group request
    $(document).on('click', '.delete-group', function (e) {
      var groupId = $(this).data('id');
      var message = "<div id='delete_box_" + groupId + "'>Deleting this group is permanent and cannot be undone. Are you sure you want to proceed? <br/><br/>";
      message += "<button class='btn btn-discard-delete-group' data-id='" + groupId + "'>Cancel</button>&nbsp;&nbsp;<button class='btn btn-confirm-delete-group' data-id='" + groupId + "'>Delete permanently</button>";
      message += "</div>";
      drawChatMessageWindow("Assistant", groupId, message, "in", "Assistant", "", true, false, "", true);
      var contactContainer = $('#group-' + groupId).find(".contactContainer");
      $(contactContainer).find('.invite-container').hide();
    });

    $(document).on('click', '.cancelInvites', function (e) {
      var groupId = $(this).data('id');
      setInviteMode(false, '', g_groupHostNameId)
      drawChatMessageWindowMenuBar(groupId, g_groupHostNameId);
      $('#' + groupId).find('.select-all-container').remove();
      $('.select-all-container').remove();
    });

    $(document).on('click', '.sendInvites', function (e) {
      var groupId = $(this).data('id');
      // guard no users to invite
      if (g_clientsPendingInvite.length === 0) {
        return;
      }
      safeInvoke("InviteToChatGroup", groupId, settings.userNameId, g_clientsPendingInvite);
      setInviteMode(false, '', g_groupHostNameId);
      $('.select-all-container').remove();
    });

    // Event handler for opening group chat windows
    $(document).on('click', '.open-group-chat', function (e) {
      e.preventDefault();
      e.stopPropagation(); // Prevent event bubbling to parent elements
      console.log('Group chat link clicked:', $(this).data('groupid'));
      var groupId = $(this).data('groupid');
      var hostNameId = $(this).data('hostnameid');
      var messageWindowsId = "group-" + groupId;
      var groupName = $(this).closest('.category').find('.categoryheader .text p').text();
      
      if ($('#usersMessagesContainer').children("#" + messageWindowsId).eq(0).length < 1) {
        safeInvoke("GetGroupHistory", settings.tenantId, groupId, 0);
        setInviteMode(false, '', hostNameId);
        drawChatMessageWindow(groupId, groupId, "", "out", groupName, "", true, true, "", true);
        // draw the menu bar for the group chat (invite links)
        drawChatMessageWindowMenuBar(groupId, hostNameId);
        // draw the filter and new chat group link in the Cleo Chat Window
        drawControlWindowMenuBar();
      }
    });

    $(document).on('click', '.select-all-container a', function (e) {
      var dataId = $(this).parent().data('id');
      // Select the .category element with the specific data-id
      var categoryElement = $("#" + dataId);   //$(".category[data-id='" + dataId + "']");
      // Find all .user elements within the .body div of the selected .category, excluding those with the 'invited' class
      var userElements = categoryElement.find(".body .user").not('.invited');
      // Do something with the userElements
      userElements.each(function () {
        var name = $(this).find('.userName p').text();
        $(this).click();
      });
    });

    // Connection state management
    var connectionState = {
      status: 'disconnected', // disconnected, connecting, connected, reconnecting
      reconnectAttempts: 0,
      maxReconnectAttempts: 10,
      isIntentionalDisconnect: false
    };

    // Initialize SignalR connection
    let connection = null;
    InitializeSignalRConnection();

    function InitializeSignalRConnection() {
      console.log('attempting to connect...' + settings.jwtToken);
      if (settings.jwtToken != null) {
        // Custom reconnect strategy with exponential backoff
        const reconnectPolicy = {
          nextRetryDelayInMilliseconds: (retryContext) => {
            connectionState.reconnectAttempts = retryContext.previousRetryCount + 1;
            
            if (retryContext.elapsedMilliseconds < 60000) {
              // First minute: retry every 2-5 seconds with jitter
              return Math.min(2000 * Math.pow(1.5, retryContext.previousRetryCount), 5000) + Math.random() * 1000;
            } else if (retryContext.elapsedMilliseconds < 300000) {
              // Next 4 minutes: retry every 5-10 seconds
              return Math.min(5000 * Math.pow(1.2, retryContext.previousRetryCount - 5), 10000) + Math.random() * 2000;
            } else {
              // After 5 minutes: retry every 30 seconds
              return 30000 + Math.random() * 5000;
            }
          }
        };

        connection = new signalR.HubConnectionBuilder()
          // .configureLogging(signalR.LogLevel.Debug)  
          .withUrl(settings.hubServer + "chathub", {
            accessTokenFactory: () => settings.jwtToken,
            transport: signalR.HttpTransportType.WebSockets | signalR.HttpTransportType.ServerSentEvents | signalR.HttpTransportType.LongPolling
          })
          .withAutomaticReconnect(reconnectPolicy)
          .build();
        
        // Set the server timeout to 2 minutes (default is 30 seconds)
        connection.serverTimeoutInMilliseconds = 120000; // 2 minutes
        
        // Connection state change handlers
        connection.onreconnecting((error) => {
          connectionState.status = 'reconnecting';
          console.log('SignalR reconnecting...', error);
          updateConnectionUI('Reconnecting... Attempt ' + connectionState.reconnectAttempts);
        });

        connection.onreconnected((connectionId) => {
          connectionState.status = 'connected';
          connectionState.reconnectAttempts = 0;
          console.log('SignalR reconnected with connectionId:', connectionId);
          updateConnectionUI('Connected');
          
          // Re-establish user connection and sync state
          connection.invoke("Connect")
            .then(() => {
              console.log('User re-connected successfully');
              // Request fresh user and group data
              syncUserState();
              // Restart heartbeat
              startHeartbeat();
            })
            .catch((err) => {
              console.error('Failed to re-establish user connection:', err);
            });
        });

        connection.onclose((error) => {
          console.log('SignalR connection closed.', error);
          
          if (!connectionState.isIntentionalDisconnect) {
            connectionState.status = 'disconnected';
            updateConnectionUI('Disconnected - Will retry connection');
            
            // If automatic reconnect failed all attempts, try manual reconnect
            if (connectionState.reconnectAttempts >= connectionState.maxReconnectAttempts) {
              console.log('Max reconnect attempts reached. Implementing fallback reconnect...');
              setTimeout(() => {
                manualReconnect();
              }, 60000); // Wait 1 minute before trying manual reconnect
            }
          }
        });
        
        // Setup all SignalR event handlers
        setupSignalREventHandlers();
        
        // Start initial connection
        startConnection();
      }
    }

    function startConnection() {
      if (!connection) {
        console.error('Connection not initialized');
        return;
      }
      
      connectionState.status = 'connecting';
      updateConnectionUI('Connecting...');
      
      connection.start()
        .then(() => {
          connectionState.status = 'connected';
          connectionState.reconnectAttempts = 0;
          console.log('SignalR connection established');
          updateConnectionUI('Connected');
          
          connection.invoke("Connect")
            .then(() => {
              // Start heartbeat after successful connection
              startHeartbeat();
            })
            .catch((err) => {
              console.error('SignalR invoke Connect failed: ', err.toString());
            });
        })
        .catch((err) => {
          connectionState.status = 'disconnected';
          console.error('SignalR connection failed: ', err.toString());
          updateConnectionUI('Connection failed - Retrying...');
          
          // Fallback manual reconnect if initial connection fails
          setTimeout(() => {
            if (connectionState.status === 'disconnected') {
              manualReconnect();
            }
          }, 5000);
        });
    }

    function manualReconnect() {
      if (!connection) {
        console.error('Connection not initialized');
        return;
      }
      
      if (connectionState.status === 'connecting' || connectionState.status === 'connected') {
        return; // Already connected or connecting
      }
      
      console.log('Attempting manual reconnect...');
      connectionState.reconnectAttempts++;
      startConnection();
    }

    function syncUserState() {
      // This function will be called after reconnection to sync state
      // For now, we'll just log it - actual implementation would request user/group lists
      console.log('Syncing user state after reconnection...');
    }

    // Heartbeat mechanism to detect connection issues early
    var heartbeatInterval;
    var heartbeatTimeout;
    var missedHeartbeats = 0;
    var maxMissedHeartbeats = 3;

    function startHeartbeat() {
      // Clear any existing intervals
      stopHeartbeat();
      
      heartbeatInterval = setInterval(() => {
        if (connection.state === signalR.HubConnectionState.Connected) {
          // Clear previous timeout
          if (heartbeatTimeout) {
            clearTimeout(heartbeatTimeout);
          }
          
          // Send heartbeat
          connection.invoke("Heartbeat")
            .then(() => {
              missedHeartbeats = 0;
              connectionState.status = 'connected';
            })
            .catch((err) => {
              console.error('Heartbeat failed:', err);
              missedHeartbeats++;
              
              if (missedHeartbeats >= maxMissedHeartbeats) {
                console.log('Too many missed heartbeats, connection seems dead');
                // Force reconnection
                connection.stop();
              }
            });
          
          // Set timeout for heartbeat response
          heartbeatTimeout = setTimeout(() => {
            missedHeartbeats++;
            if (missedHeartbeats >= maxMissedHeartbeats) {
              console.log('Heartbeat timeout - connection may be dead');
              updateConnectionUI('Connection unstable');
            }
          }, 5000); // 5 second timeout for heartbeat response
        }
      }, 30000); // Send heartbeat every 30 seconds
    }

    function stopHeartbeat() {
      if (heartbeatInterval) {
        clearInterval(heartbeatInterval);
        heartbeatInterval = null;
      }
      if (heartbeatTimeout) {
        clearTimeout(heartbeatTimeout);
        heartbeatTimeout = null;
      }
      missedHeartbeats = 0;
    }

    // Setup SignalR event handlers (must be called after connection is created)
    function setupSignalREventHandlers() {
      if (!connection) {
        console.error('Cannot setup event handlers - connection not initialized');
        return;
      }
      
      // Add generic event logger to catch all SignalR events
      const originalOn = connection.on;
      connection.on = function(methodName, callback) {
        const wrappedCallback = function(...args) {
          if (methodName !== 'heartbeatAck') { // Don't log heartbeat to reduce noise
            console.log(`SignalR Event Received: ${methodName}`, args);
          }
          return callback.apply(this, args);
        };
        return originalOn.call(this, methodName, wrappedCallback);
      };
      
      // Heartbeat acknowledgment handler
      connection.on("heartbeatAck", function () {
        // Reset missed heartbeats counter when we receive an acknowledgment
        missedHeartbeats = 0;
        if (heartbeatTimeout) {
          clearTimeout(heartbeatTimeout);
          heartbeatTimeout = null;
        }
      });
      
      // All other SignalR event handlers will be set up here
      setupChatEventHandlers();
    }

    function updateConnectionUI(message) {
      // Update the UI to show connection status
      if (message.includes('Connected')) {
        $("#windowSpacer").attr("style", "background-color: green");
        $("#windowSpacer p").html("Online");
      } else if (message.includes('Reconnecting') || message.includes('Connecting')) {
        $("#windowSpacer").attr("style", "background-color: orange");
        $("#windowSpacer p").html(message);
      } else {
        $("#windowSpacer").attr("style", "background-color: red");
        $("#windowSpacer p").html("Offline");
      }
    }

    // Functions
    // Cleo Chat Window Fucntions - These are functions for the main chat window that contains the filter and user groups/users

    // Redraws the Cleo Chat Window with the passed in user
    // When notifyClientConnected is fired this is called for each user that exists so the window is updated with the new user
    function redrawControlWindow(user) {
      console.log(user)
      // draw the category for the passed in user
      var parentContainer = $("#usersWindowContainer #usersWindow #groups");
      var selectedContainer = $(sanitizeContainerName(user.container));
      // if the selected container does not exist then draw it
      if ($(selectedContainer).length < 1) {
        selectedContainer = drawControlWindowGroupCategory(parentContainer, user.containerId, user.container, null);
      }
      // draw the passed in user
      drawControlWindowUser($(selectedContainer).find(".body"), user);
    }

    function sanitizeContainerName(name) {
      var sanitizedName = name.replace("(", " ").replace(")", " ").replace("-", " ").replace(/ /g, "");
      return sanitizedName;
    }

    function drawControlWindowUser(container, user) {
      user.nameId = CSS.escape(user.nameId);
      if ($("#" + user.nameId).length < 1) {
        // Use default avatar or dynamic avatar based on feature toggle
        var avatarSrc = "css/images/avatar.jpg"; // Default avatar
        checkAvatarUploadFeature().then(function(isEnabled) {
          if (isEnabled) {
            $("#" + user.nameId + " img").first().attr("src", settings.hubServer + "avatar/" + settings.tenantId + "/" + user.nameId);
          }
        });
        
        var userHtml = $("<div id=\"" + user.nameId + "\" class=\"user\"><img src=\"" + avatarSrc + "\"/><div class=\"userName\"><img class=\"dot dot0\" alt=\"\" /><p>" + user.name + "</p><p class=\"time\">" + user.time + "</p></div><p class=\"status role\">" + user.location + "</p><p>&nbsp;</p></div>");
        $(container).append(userHtml);
        // Click handler for when a user is clicked - use event delegation to avoid duplicate handlers
        $(userHtml).off('click').on('click', function () {
          console.log('User clicked:', user.nameId, 'Invite mode:', _isInviteMode);
          if (user.status == 0 || user.status == 2) {
            if (!_isInviteMode) {
              // check if the chat message window is already open
              if ($('#usersMessagesContainer').children("#group-" + user.nameId).eq(0).length < 1) {
                console.log('open window and restore history!');
                drawChatMessageWindow(user.nameId, settings.userNameId, "", "out", user.name, settings.userName, false, true, "", true);
                safeInvoke("GetHistory", settings.tenantId, user.nameId, settings.userNameId, 0);
              } else {
                console.log('window already open, no history loaded.')
              }
            } else {
              // check if user has already been invited
              var userInvitedIndex = g_clientsPendingInvite.indexOf(user.nameId);
              if (userInvitedIndex !== -1) {
                // already added so de-select
                g_clientsPendingInvite.splice(userInvitedIndex, 1);
                $('#' + user.nameId).removeClass('invited');
              } else {
                // add to users to invite array                             
                g_clientsPendingInvite.push(user.nameId);
                $('#' + user.nameId).addClass('invited');
              }
              // redraw chat menu bar
              drawChatMessageWindowMenuBar(g_inviteToGroupId, g_groupHostNameId);
            }
          }
        });
      } else {
        $("#" + userNameId).find(".role").html(userRole);
        $("#" + userNameId).find(".base").html(userBase);
      }
    }

    function toggleCategory(categoryHeader) {
      if ($(categoryHeader).parent().find(".body").eq(0).is(":visible")) {
        $(categoryHeader).parent().find(".body").eq(0).slideUp(function () {
          $(categoryHeader).parent().find(".body").eq(0).removeClass("down");
          $(categoryHeader).removeClass("down");
        });
      } else {
        $(categoryHeader).parent().find(".body").eq(0).slideDown();
        $(categoryHeader).parent().find(".body").eq(0).addClass("down");
        $(categoryHeader).addClass("down");
      }
    }

    // Draws the category container and populates the category header
    function drawControlWindowGroupCategory(container, element, title, group) {
      var groupClassName = (group != null) ? " group" : "";
      if ($("#" + container.attr("id") + " #" + element).length < 1) {
        return $("<div id=" + element + " class=\"category" + groupClassName + "\"><div class=\"categoryheader\"><div class=\"text\"><p>" + title + "</p></div></div><div class=\"body\"></div></div>").appendTo($(container));
      } else {
        return $("#" + element);
      }
    }

    // Draws the Chat Group into the Cleo Chat Window, 
    // This consists of the menu bar, link to open the group and the users associated with the group.
    // Takes in the group to be drawn and the users to be populated in the group (users to come)
    function drawControlWindowChatGroup(group) {
      console.log('drawControlWindowChatGroup called for group:', group.name, 'ID:', group.groupId);
      // var parentContainer = $("#usersWindowContainer #usersWindow #groups");
      var parentContainer = $("#usersWindowContainer #usersWindow #groups");
      var container = group.name;
      var selectedContainer = $(CSS.escape(container));
      var isNewGroup = $(selectedContainer).length < 1;
      
      console.log('Group container:', container, 'isNewGroup:', isNewGroup, 'selectedContainer length:', selectedContainer.length);
      
      if (isNewGroup) {
        selectedContainer = drawControlWindowGroupCategory(parentContainer, group.groupId, container, group);
        console.log('Created new group category, selectedContainer:', selectedContainer);
      }
      
      // Always update the host information for the group container
      selectedContainer.attr('data-hostnameid', group.hostNameId);
      sortCategories();
      
      // Check if current user is newly added to this group (not left)
      var currentUserInGroup = group.users.find(function(user) {
        return user.userNameId === settings.userNameId && !user.leftGroup;
      });
      
      console.log('Current user in group:', currentUserInGroup, 'Invite mode:', _isInviteMode);
      
      // Hide this group if we're in invite mode (hide all groups during invite mode)
      // BUT don't hide groups where the current user just joined/rejoined
      if (_isInviteMode && !isNewGroup && !currentUserInGroup) {
        console.log('Hiding group due to invite mode');
        selectedContainer.hide();
      } else {
        console.log('Showing group');
        selectedContainer.show();
      }
      // Clear existing body content first to avoid duplicates
      $(selectedContainer).find(".body").empty();
      
      // the link to Open the Message Window for this group chat
      var openGroupChatLink = $("<div id=\"group-link-" + group.groupId + "\" class=\"group-chat-link\" style=\"display: block; margin: 5px 0; padding: 5px; background-color: #f0f0f0; border-radius: 3px;\"><div class=\"userName\"><p><a href='#' class=\"link underline open-group-chat\" style='font-size:12px' data-hostnameid='" + group.hostNameId + "' data-groupid='" + group.groupId + "'>Open Chat</a></p></div></div>");
      $(selectedContainer).find(".body").append(openGroupChatLink);
      
      // show the users of the group
      var html = '';
      for (var i = 0; i < group.users.length; i++) {
        var isHost = (group.users[i].userNameId === group.hostNameId) ? true : false;
        var dot = "";
        switch (group.users[i].status) {
          case 0:
            dot = "dot0";
            break;
          case 1:
            dot = "dot1";
            break;
          case 2:
            dot = "dot2";
            break;
        }
        if (!group.users[i].leftGroup) {
          html += "<div class=\"user\">";
          html += "<img src=\"css/images/avatar.jpg\"/>";
          html += "<div class=\"userName\" data-id='" + group.users[i].userNameId + "'>";
          html += "<img class=\"dot " + dot + "\" alt=\"\" />";
          html += "<p>";
          html += group.users[i].userName
          if (isHost) {
            html += " (Host)";
          }
          html += "</p>";
          if (!isHost && group.hostNameId === settings.userNameId) {
            html += "<br/><div style='margin: 5px 15px 0px 11px; text-align:right;'><a href='#' class='link remove-link' data-groupid='" + group.groupId + "' data-id='" + group.users[i].userNameId + "'>Remove</a></div>";
          }
          html += "</div>";
          html += "&nbsp;&nbsp;" // required
          html += "<p>&nbsp;</p>"; // required
          html += "</div>";
        }
      };
      $(selectedContainer).find(".body").append(html);
      
      // Update avatars in group if feature is enabled
      checkAvatarUploadFeature().then(function(isEnabled) {
        if (isEnabled) {
          for (var i = 0; i < group.users.length; i++) {
            if (!group.users[i].leftGroup) {
              var userElement = $(selectedContainer).find(".body .user").eq(i); // No need to +1 since "Open Chat" link is no longer .user class
              userElement.find("img").first().attr("src", settings.hubServer + "avatar/" + settings.tenantId + "/" + group.users[i].userNameId);
            }
          }
        }
      });
    }

    function sortCategories() {
      var $groups = $('#groups');
      // Detach and store elements with both category and group classes
      var $categoryGroup = $groups.children('.category.group').detach();
      // Detach and store elements with only the category class
      var $category = $groups.children('.category').not('.group').detach();
      // Sort the elements based on the text in the .categoryheader .text p
      function sortElements($elements) {
        return $elements.sort(function (a, b) {
          var aText = $(a).find('.categoryheader .text p').text().toLowerCase();
          var bText = $(b).find('.categoryheader .text p').text().toLowerCase();
          return aText.localeCompare(bText);
        });
      }
      // Sort the detached elements
      $categoryGroup = sortElements($categoryGroup);
      $category = sortElements($category);
      // Add the group-spacer class to the last element in $categoryGroup
      if ($categoryGroup.length > 0) {
        $categoryGroup.removeClass('group-spacer');
        $categoryGroup.last().addClass('group-spacer');
      }
      // Re-append the menuBar, followed by the sorted categoryGroup elements, then the sorted remaining category elements
      $groups.children('#menuBar').after($categoryGroup);
      $groups.append($category);
    }

    // Draws the Cleo Chat Window Menu Bar which contains the Filter and New Chat Group link
    function drawControlWindowMenuBar() {
      if ($('.filter-container').length < 1) {
        var html = '<div id="menuBar"><div class="filter-container">';
        html += '<input id="filter" class="filter" type="text" placeholder="Filter Contacts">';
        html += '</div>';
        html += '<div class="new-group-container">';
        html += '<span class="link">+</span>&nbsp;<span class="link underline">New Chat Group</span>';
        html += '</div></div>';

        // $("<div/>").attr("id", "menuBar").html($(html)).prepend($(groups));

        $("#groups").prepend(html);

      }
    }

    // Clears the Cleo Chat Window Menu Bar, called when the user count drops below 2 users
    function clearControlWindowMenuBar() {
      var menuBar = $('#menuBar');
      if (menuBar.length) {
        menuBar.remove();
      }
    }

    // locgic to filter the users in the Cleo Chat Window
    function filterControlWindowUsers(filterString) {
      var matchedCategories = [];
      // If the filter string is empty, clear the highlights and close all categories
      if (filterString === "") {
        $('.category').each(function () {
          var categoryHeader = $(this).find('.categoryheader');
          var categoryBody = $(this).find('.body');
          categoryBody.slideUp(function () {
            categoryBody.removeClass('down');
            categoryHeader.removeClass('down');
          });
          categoryBody.find('.user').each(function () {
            $(this).show();
            $(this).find('.userName p:first, .status.role, .status.base').each(function () {
              $(this).html(function (_, html) {
                return html.replace(/<span class="highlight">|<\/span>/g, '');
              });
            });
          });
          categoryHeader.show();
        });
        return matchedCategories;
      }

      // Create a case-insensitive regex for the filter string
      var regex = new RegExp(`(${filterString})`, 'ig');

      // Iterate over each category
      $('.category').each(function () {
        var category = $(this);
        var categoryHeader = category.find('.categoryheader');
        var categoryBody = category.find('.body');
        var hasMatchedUser = false;

        // Select all divs with class 'user' inside the current category's 'body'
        categoryBody.find('.user').each(function () {
          var userNameElement = $(this).find('.userName p:first');
          var roleElement = $(this).find('.status.role');
          var baseElement = $(this).find('.status.base');
          var userName = userNameElement.text();
          var role = roleElement.text();
          var base = baseElement.text();
          var userNameMatched = userName.toLowerCase().includes(filterString.toLowerCase());
          var roleMatched = role.toLowerCase().includes(filterString.toLowerCase());
          var baseMatched = base.toLowerCase().includes(filterString.toLowerCase());
          if (userNameMatched || roleMatched || baseMatched) {
            // Highlight the matched text in username, role, and base
            if (userNameMatched) {
              var highlightedUserName = userName.replace(regex, '<span class="highlight">$1</span>');
              userNameElement.html(highlightedUserName);
            }
            if (roleMatched) {
              var highlightedRole = role.replace(regex, '<span class="highlight">$1</span>');
              roleElement.html(highlightedRole);
            }
            if (baseMatched) {
              var highlightedBase = base.replace(regex, '<span class="highlight">$1</span>');
              baseElement.html(highlightedBase);
            }
            // Show the user if it matches
            $(this).show();
            hasMatchedUser = true;
          } else {
            $(this).hide();
          }
          // remove highlights if the user doesn't match
          if (!userNameMatched) {
            userNameElement.html(function (_, html) {
              return html.replace(/<span class="highlight">|<\/span>/g, '');
            });
          }
          // remove highlights if the role doesn't match
          if (!roleMatched) {
            roleElement.html(function (_, html) {
              return html.replace(/<span class="highlight">|<\/span>/g, '');
            });
          }
          // remove highlights if the base doesn't match
          if (!baseMatched) {
            baseElement.html(function (_, html) {
              return html.replace(/<span class="highlight">|<\/span>/g, '');
            });
          }
        });
        if (hasMatchedUser) {
          // If there's a matched user, show the category
          if (!categoryBody.hasClass('down')) {
            categoryBody.addClass('down').slideDown();
            categoryHeader.addClass('down');
          }
          // Store the category id
          var categoryId = category.attr('id');
          if (!matchedCategories.includes(categoryId)) {
            matchedCategories.push(categoryId);
          }
          $(categoryHeader).show();
        } else {
          // If no users matched, close the category
          categoryBody.slideUp(function () {
            categoryBody.removeClass('down');
            categoryHeader.removeClass('down');
          });

          $(categoryHeader).hide();
        }
      });
      return matchedCategories;
    }

    // Chat Message Window functions - This is the popup message window the users use to communicate with each other
    function drawChatMessageWindow(to, from, message, direction, targetName, senderName, isGroupChat, allowChatBar, dt, setFocus = false) {
      var messageWindowsId;
      if (isGroupChat) {
        // For group chats, prioritize using the actual group ID over "Assistant"
        // If both parameters are the same, use either one
        if (to === from) {
          messageWindowsId = "group-" + to;
        } else if (to === "Assistant") {
          messageWindowsId = "group-" + from;
        } else if (from === "Assistant") {
          messageWindowsId = "group-" + to;
        } else {
          // If neither is "Assistant", use the direction-based logic
          messageWindowsId = "group-" + (direction == "out" ? to : from);
        }
      } else {
        // For one-to-one chats, use the existing logic
        messageWindowsId = "group-" + (direction == "out" ? to : from);
      }
      // remove any spaces
      messageWindowsId = messageWindowsId.replace(/ /g, "");
      if (direction == "in") {
        var tmp = from;
        from = to;
        to = tmp;
        tmp = senderName;
        senderName = targetName;
        targetName = tmp;
      }
      var userMessageContainer = $("#usersMessagesContainer");
      var messageWindow = $(userMessageContainer).children("#" + messageWindowsId).eq(0);
      // this will be true if new chat window is being created
      if ($(messageWindow).length < 1) {
        messageWindow = $("<div id=\"" + messageWindowsId + "\" class=\"messageWindow\"></div>");
        $(userMessageContainer).append(messageWindow);
        var tab = $("<div class=\"messageTab\"><div class=\"tabContent\"><div class=\"user\"><p>" + targetName + "</p></div></div><div class=\"close " + (direction == "out" ? "down" : "") + "\">&nbsp;</div></div>");
        // if the chat bar is allowed then make the messageContentContainer small class needs to be applied
        var messageContentContainer = (!allowChatBar) ?
          $("<div class=\"messageContentContainer small\"></div>") :
          $("<div class=\"messageContentContainer\"></div>")
        $(messageWindow).append(messageContentContainer);
        $(messageWindow).append(tab);
        // find the contactContainer
        var contactContainer = $("<div class=\"contactContainer\" data-id='" + messageWindowsId + "'></div>");
        
        // For one-to-one chats, store the other user's ID for avatar updates
        if (!isGroupChat) {
          var otherUserId = (direction == "out") ? to : from;
          contactContainer.data("userid", otherUserId);
        }
        var contactHeader = (isGroupChat)
          ? $("<div class=\"contactHeader " + settings.tenantId + "\"><span class='group-img'></span><p>" + targetName + "</p><div class=\"minimizeChatContainer\"><div>&nbsp;</div></div><div class=\"closeOpen\"><div>&nbsp;</div></div></div>")
          : $("<div class=\"contactHeader  " + settings.tenantId + "\"><img src=\"css/images/avatar.jpg\"/><p>" + targetName + "</p><div class=\"minimizeChatContainer\"><div>&nbsp;</div></div><div class=\"closeOpen\"><div>&nbsp;</div></div></div>");
        var contactSpacer = $("<div class=\"contactSpacer\"></div>");
        $(contactContainer).append(contactHeader);
        $(contactContainer).append(contactSpacer);
        
        // Update contact header avatar if feature is enabled (only for non-group chats)
        if (!isGroupChat) {
          checkAvatarUploadFeature().then(function(isEnabled) {
            if (isEnabled) {
              var otherUserId = contactContainer.data("userid");
              var avatarUrl = settings.hubServer + "avatar/" + settings.tenantId + "/" + otherUserId;
              console.log('Setting one-to-one chat avatar:', avatarUrl, 'for user:', otherUserId);
              $(contactHeader).find("img").attr("src", avatarUrl);
            } else {
              console.log('Avatar feature is disabled, using default avatar for one-to-one chat');
            }
          });
        }
        var conversationContainer = $("<div class=\"conversationContainer border-bottom\"></div>");
        $(messageContentContainer).append(contactContainer);
        $(messageContentContainer).append(conversationContainer);
        if (allowChatBar) {
          var inputContainer = $("<div class=\"inputContainer\"></div>");
          var groupId = messageWindowsId.replace("group-", "");
          var input = $("<input class='chat-input' type=\"text\" data-isgroupchat='" + isGroupChat + "' data-id='" + groupId + "' maxlength='2000'</input>");
          $(messageContentContainer).append(inputContainer);
          $(inputContainer).append(input);
          $(input).focus();
        }
        $(".user").click(function () {
          var messageContainer = $(this).closest(".messageWindow").find(".messageContentContainer");
          if (!$(messageContainer).is(":visible")) {
            $(messageContainer).parent().find(".close").eq(0).addClass("down");
            $(messageContainer).slideDown(function () {
              var container = $(messageContainer).find(".conversationContainer").eq(0);
              $(container).scrollTop($(container)[0].scrollHeight);
              $(messageContainer).find(".inputContainer").find("input").focus();
            });
          }
        });

        $(".minimizeChatContainer").click(function () {
          var messageContainer = $(this).closest(".messageWindow").find(".messageContentContainer");
          if ($(messageContainer).is(":visible")) {
            $(messageContainer).slideUp(function () {
              $(messageContainer).parent().find(".close").eq(0).removeClass("down");
            });
          }
        });

      } else {
        var inputContainer = $("<div class=\"inputContainer\"></div>");
        if (!allowChatBar) {
          $(messageWindow).find('.inputContainer').hide();
          var messageBody = $(messageWindow).find(".messageContentContainer");
          $(messageBody).addClass('no-chat-bar');
        }
      }
      var messageBody = $(messageWindow).find(".messageContentContainer");
      if (message != "") {
        drawMessage($(messageWindow).find(".conversationContainer").eq(0), message, direction, senderName, dt);
      }
      var messageBody = $(messageWindow).find(".messageContentContainer");
      var flashId;
      if (direction === "in") {
        if (!$(messageBody).is(":visible")) {
          var messageConent = $(messageBody).parent().find(".tabContent").eq(0);
          flashId = setInterval(function () {
            flash(messageBody, messageConent);
          }, 800);
          $(".user").click(function () {
            if (!isGroupChat) {
              safeInvoke("GetHistory", settings.tenantId, senderName.replace(/\s+/g, ''), settings.userNameId, 0);
            } else {
              // group chat
              var isInvite = message.includes("Hi! You're invited to join");
              // only get history if not an invite, history will be got after invite is accepted
              if (!isInvite) {
                safeInvoke("GetGroupHistory", settings.tenantId, to, 0);
              }
            }
            clearFlash(flashId, messageConent);
          });
        }
      }
      if (setFocus) {
        $(messageBody).show();
        $(messageWindow).find("input").eq(0).focus();
      }
    }

    // Draws the message into the message window
    function drawMessage(container, message, direction, from, dt) {
      var currentDT = new Date($.now());
      if (dt !== "") {
        currentDT = new Date(dt);
      }
      const day = String(currentDT.getDate()).padStart(2, '0');
      const month = String(currentDT.getMonth() + 1).padStart(2, '0'); // getMonth() returns month from 0 to 11
      const year = String(currentDT.getFullYear()).slice(-2);
      const hours = String(currentDT.getHours()).padStart(2, '0');
      const minutes = String(currentDT.getMinutes()).padStart(2, '0');
      let formattedDate = `${day}/${month}/${year} ${hours}:${minutes}`;
      if (isToday(currentDT)) {
        formattedDate = `${hours}:${minutes}`;
      }
      outerMessage = $("<div class=\"outerMessage\" data-message-date=\"" + dt + "\"><div class=\"header\"><div class=\"name " + (direction === "out" ? "out" : "") + "\"><p>" + from + "</p></div><div class=\"time\">" + formattedDate + "</div></div><div class=\"message\">" + message + "</div></div>");
      $(container).append(outerMessage);
      $(container).scrollTop($(container)[0].scrollHeight);
    }

    function closeMessageWindow(messageContainer) {
      var message = $(messageContainer).closest(".messageWindow");
      var container = $(message).find(".messageContentContainer");
      if ($(container).is(":visible")) {
        $(container).slideDown(function () {
          $(message).remove();
        });
      } else {
        $(message).remove();
      }
    }

    function isActive() {
      return connection.state === signalR.HubConnectionState.Connected && 
             connectionState.status === 'connected';
    }

    // Safely invoke server methods with automatic queueing
    function safeInvoke(methodName, ...args) {
      if (isActive()) {
        console.log(`Invoking server method: ${methodName} with args:`, args);
        return connection.invoke(methodName, ...args)
          .then((result) => {
            console.log(`Successfully invoked ${methodName}, result:`, result);
            return result;
          })
          .catch((err) => {
            console.error(`Error invoking ${methodName}:`, err);
            // If it's a connection error, the reconnect logic will handle it
            if (err.toString().includes('connection') || err.toString().includes('disconnected')) {
              console.log('Connection error detected, reconnection will be attempted');
            }
          });
      } else {
        console.log(`Cannot invoke ${methodName} - connection not active. Current state: ${connectionState.status}`);
        // Could implement a queue here to retry when connected
        return Promise.reject('Connection not active');
      }
    }

    function clearFlash(intervalId, flashElement) {
      clearInterval(intervalId);
      $(flashElement).removeClass("flash");
    }

    function flash(watchElement, flashElement) {
      if ($(watchElement).is(":visible")) {
        clearInterval(this);
      }
      if ($(flashElement).hasClass("flash")) {
        $(flashElement).removeClass("flash");
      } else {
        $(flashElement).addClass("flash");
      }
    }

    function onGroupCreated(group) {
      var contactContainer = $('.contactContainer').find('[data-id="' + group.groupId + '"]');;
      var contactHeader = $('.contactContainer').closest('.contactHeader');
      // find the contactHeader and update the group name
      $('#group-NewChatWindow .contactHeader p').text(group.name);
      $('#group-NewChatWindow .user p').text(group.name);
      // update the chat window id from group-Assistant to the new assigned group id
      $('#group-NewChatWindow').attr('id', 'group-' + group.groupId);
      // update the contact container
      $('.contactContainer[data-id="group-NewChatWindow"]').attr('data-id', group.groupId);
      $('#group-' + group.groupId + ' .chat-input').attr('data-id', group.groupId);
      // update the chat input box group id
      $(contactContainer).closest('.conversationContainer').html("");
      $(contactContainer).html(contactHeader);
      drawChatMessageWindow(group.groupId, group.groupId, "Now to invite one or more users, click the '+ Invite user(s)' link above", "in", "Assistant", "Assistant", true, true, "");
      drawChatMessageWindowMenuBar(group.groupId, group.hostNameId);
      // set the length of the chat allowed back to 2000 - as was restricted to 16 for the chat name
      $('.chat-input[data-id="' + group.groupId + '"]').attr('maxLength', 2000);
      $('.new-group-container').show();
    }

    // Draw the menu bar for the group chat (Invite User(s), Send Invite, Cancel )
    function drawChatMessageWindowMenuBar(groupId, hostNameId) {
      var contactContainer = $('#group-' + groupId).find(".contactContainer");
      var html = "";
      if (_isInviteMode) {
        html += "<div class='invite-container'>";
        html += "<div class='inviting-bar'>";
        html += "<span class='link underline'>" + g_clientsPendingInvite.length + " user";
        if (g_clientsPendingInvite.length !== 1) {
          html += "s";
        }
        html += " selected</span>";
        if (g_clientsPendingInvite.length > 0) {
          html += "&nbsp;|&nbsp;<span class='sendInvites link underline' data-id='" + groupId + "'>Send Invite</span>";
        }
        html += "&nbsp;|&nbsp;<span class='cancelInvites link underline' data-id='" + groupId + "'>Cancel</span>";
        html += "</div>";
        html += "</div>";
      } else {
        html += "<div class='invite-container'>";
        // Use the passed hostNameId if available, otherwise fall back to g_groupHostNameId
        var currentHostNameId = hostNameId || g_groupHostNameId;
        if (settings.userNameId === currentHostNameId) {
          html += "<div class='delete-group' data-id='" + groupId + "'><span></span>&nbsp;<span class='link underline'>Delete group</span></div>";
        } else {
          html += "<div class='leave-group' data-id='" + groupId + "'><span></span>&nbsp;<span class='link underline'>Leave group</span></div>";
        }
        html += "<div class='invite-users' data-id='" + groupId + "'><span class='link'>+</span>&nbsp;<span class='link underline'>Invite user(s)</span></div>";
        html += "</div>";
      }
      $(contactContainer).find('.invite-container').remove();
      $(contactContainer).append(html);
    }

    function enableMessageWindow(userNameId) {
      var windowId = "#group-" + userNameId;
      if ($(windowId).length > 0) {
        $(windowId).find(".contactHeader p").html($(windowId).find(".contactHeader p").html().replace(" - Offline", ""));
        $(windowId).find("input").removeAttr("disabled");
        $(windowId).find("input").attr("placeholder", "");
        $(windowId).find(".conversationContainer").removeAttr("style");
      }
    }

    function updateStatus(userNameId, status) {
      if (!isActive()) return;
      var user = $("#" + userNameId);
      var dot = $(user).find(".dot");
      $(dot).attr("alt", status);
      $(dot).removeClass("dot0");
      $(dot).removeClass("dot1");
      $(dot).removeClass("dot2");
      $(dot).addClass("dot" + status);
      // update for any groups
      $('[data-id="' + userNameId + '"]').find('.dot')
        .removeClass('dot0')
        .removeClass("dot1")
        .removeClass("dot2")
        .addClass("dot" + status);
    }

    function disableMessageWindow(userNameId) {
      var windowId = "#group-" + userNameId;
      if ($(windowId).length > 0) {
        if ($(windowId).find('.contactHeader p').html().indexOf(" - Offline") > 0) {
          return;
        }
        $(windowId).find('.contactHeader p').html($(windowId).find('.contactHeader p').html() + " - Offline");
        $(windowId).find('.conversationContainer').attr('style', 'background-color:#eee;');
        $(windowId).find("input").attr("disabled", "disabled");
        $(windowId).find("input").attr("placeholder", "User has left the conversation");
      }
    }

    function eraseUser(userNameId) {
      var user = $("#" + userNameId);
      var category = user.closest(".category");
      $("#" + userNameId).remove();
      var siblings = $(category).find(".user").length;
      if (siblings < 1) {
        category.remove();
      }
      disableMessageWindow(userNameId);
    }

    // helper functions

    function capitaliseWords(str) {
      return str.split(' ').map(function (word) {
        return word.charAt(0).toUpperCase() + word.slice(1).toLowerCase();
      }).join(' ');
    }

    function isToday(date) {
      const today = new Date();
      return date.getFullYear() === today.getFullYear() &&
        date.getMonth() === today.getMonth() &&
        date.getDate() === today.getDate();
    }

    function groupChatWindowVisible(groupId) {
      var messageContainer = $('div[data-id="' + "group-" + groupId + '"]').closest('.messageContentContainer');
      if ($(messageContainer).is(":visible")) {
        return true;
      }
      return false;
    };

    function onLeaveGroupRequest(groupId) {
      var message = "<div id='leave_box_" + groupId + "'>Are you sure you want to leave the group? <br/><br/>";
      message += "<button class='btn btn-discard-leave-group' data-id='" + groupId + "'>Cancel</button>&nbsp;&nbsp;<button class='btn btn-confirm-leave-group' data-id='" + groupId + "'>Leave group</button>";
      message += "</div>";
      drawChatMessageWindow("Assistant", groupId, message, "in", "Assistant", "", true, false, "", true);
      var contactContainer = $('#group-' + groupId).find(".contactContainer");
      $(contactContainer).find('.invite-container').hide();
    }

    // SignalR methods
    function setupChatEventHandlers() {
      // Notification that my connection has connected.
      // Sets the online contacts section to indiaate the number of online contacts
      // Draws the Cleo Chat Pannel once for each user in the users list, this ensures they are all drawn correctly
      connection.on("notifyMyConnectionConnected", function (users, client) {
        // if the host is not active then return
        if (!isActive()) return;
        $("#userDetails p").html(client.name);
        if (users.length > 1) {
          $("#usersWindowHeader p").html("Online Contacts (" + (users.length - 1) + ")");
        } else {
          $("#usersWindowHeader p").html("No Online Contacts");
        }
        $("#windowSpacer").attr("style", "background-color: green");
        $("#windowSpacer p").html("Online");
        // Loop round each user and create the tree (container + user)
        try {

          for (var i = 0; i < users.length; i++) {
            if (users[i].connectionId !== client.connectionId) {
              console.log(users[i])
              redrawControlWindow(users[i]);
            }
          }
        } catch (e) {
          console.error("Error in notifyMyConnectionConnected: ", e);
        }
        drawControlWindowMenuBar();
      });

    // Notification that a New Client has Connected.
    // this applys to all clients accept the client that has connected
    // Sets the online contacts section to indiaate the number of online contacts
    // Draws the Cleo Chat Pannel once 
      connection.on("notifyNewClientConnected", function (userCount, user) {
        if (user.nameId === settings.userNameId) return;
        if (!isActive()) return;
        if (userCount > 1) {
          $("#usersWindowHeader p").html("Online Contacts (" + (userCount - 1) + ")");
        } else {
          $("#usersWindowHeader p").html("No Online Contacts");
        }
        drawControlWindowMenuBar();
        redrawControlWindow(user);
        enableMessageWindow(user.nameId);
        
        // Notify in any open chat windows that the user has reconnected
        var chatWindowId = "#group-" + user.nameId;
        if ($(chatWindowId).length > 0) {
          var currentTime = new Date();
          var reconnectionMessage = user.name + " has reconnected to the conversation.";
            drawMessage($(chatWindowId).find(".conversationContainer").eq(0), reconnectionMessage, "in", "Assistant", currentTime);
        }
      });

      connection.on("notifyUserStatusChanged", function (userNameId, status) {
        if (!isActive()) return;
        updateStatus(userNameId, status);
      });

      connection.on("removeUser", function (userNameId, userCount) {
        if (!isActive()) return;
        
        // Remove the user from pending invite list if they were selected
        var userInvitedIndex = g_clientsPendingInvite.indexOf(userNameId);
        if (userInvitedIndex !== -1) {
          g_clientsPendingInvite.splice(userInvitedIndex, 1);
          
          // Update the invite menu bar to reflect the new count
          if (_isInviteMode) {
            drawChatMessageWindowMenuBar(g_inviteToGroupId, g_groupHostNameId);
          }
        }
        
        eraseUser(userNameId);
        if (userCount > 1) {
          $("#usersWindowHeader p").html("Online Contacts (" + (userCount - 1) + ")");
        } else {
          $("#usersWindowHeader p").html("No Online Contacts");
        }
        if (userCount < 2) {
          clearControlWindowMenuBar();
        }
      });

      connection.on("userDisconnectedFromChat", function (disconnectedUserNameId, disconnectedUserName, message) {
        if (!isActive()) return;
        
        // Check if there's an open chat window with this user
        var chatWindowId = "#group-" + disconnectedUserNameId;
        if ($(chatWindowId).length > 0) {
          // Display the disconnection message in the chat window
          var currentTime = new Date();
            drawMessage($(chatWindowId).find(".conversationContainer").eq(0), message, "in", "Assistant", currentTime);
          
          // Disable the chat input
          disableMessageWindow(disconnectedUserNameId);
        }
      });

      connection.on("disconnect", function () {
        state = "closed";
        connectionState.status = 'disconnected';
        connectionState.isIntentionalDisconnect = true;
        stopHeartbeat();
        
        $('#groups').html('');
        $('#usersWindow').slideUp();
        $('#usersWindowHeader').unbind('click');
        $("#windowSpacer").attr("style", "background-color: red");
        $('#windowSpacer p').html('OffLine');
        $('#usersWindowHeader p').html('You were logged off');
      });

      connection.on("inviteToGroup", function (inviteFrom, groupId, groupName) {
        console.log('=== INVITE TO GROUP RECEIVED ===');
        console.log('Invite From:', inviteFrom);
        console.log('Group ID:', groupId);
        console.log('Group Name:', groupName);
        console.log('Current User:', settings.userName);
        
        var message = "Hi! You're invited to join " + groupName + ". Click 'Accept' to join our group!";
        message += "<br/><br/><button class='btn accept' data-id='" + groupId + "'>Accept Invite</button>&nbsp;&nbsp<button class='btn decline' data-id='" + groupId + "'>Decline</button><br/><br/>";
        // drawChatMessageWindow(groupId, inviteFrom, message, "in", inviteFrom, groupName, true, false);
        drawChatMessageWindow(groupId, groupId, message, "in", inviteFrom, groupName, true, false, "");
        console.log('Invite message window drawn');
      });

      connection.on("inviteToGroupSent", function (group, message) {
        setInviteMode(false, '', group.hostNameId);
        drawChatMessageWindow(group.groupId, group.groupId, message, "in", settings.userName, group.name, true, true, "");
        var isHost = (settings.userNameId === group.hostNameId) ? true : false;
        drawChatMessageWindowMenuBar(group.groupId, group.hostNameId);
      });

      connection.on("notifyHostOfUsersAlreadyInGroup", function (usersInGroup, group) {
        var usersString = "";
        for (var i = 0; i < usersInGroup.length; i++) {
          usersString += usersInGroup[i].name + ",";
        };
        // remove trailing comma
        usersString = usersString.slice(0, -1);
        var message = (usersInGroup.length > 1) ?        usersString + " are already in the group!" :        usersString + " is already in the group!";
        setInviteMode(false, '', group.hostNameId);
        drawChatMessageWindow(group.groupId, group.groupId, message, "in", "Assistant", group.groupId, true, true, "");
        drawChatMessageWindowMenuBar(group.groupId, group.hostNameId);
      });

      connection.on("groupExists", function (group) {
      drawChatMessageWindow("Assistant", "NewChatWindow", "A group with that name already exists, please choose another.", "in", "Assistant", "New Group Chat", "", true, "", true);
    });

      connection.on("groupNameTooShortOrTooLong", function (group) {
      drawChatMessageWindow("Assistant", "NewChatWindow", "Please enter a group name between 3 and 18 characters.", "in", "Assistant", "New Group Chat", "", true, "", true);
    });

      connection.on("groupCreated", function (group) {
      drawControlWindowChatGroup(group);
      onGroupCreated(group);
    });

      connection.on("receiveMessage", function (userNameId, message, targetName, senderName) {
        if (!isActive()) return;
        drawChatMessageWindow(settings.userNameId, userNameId, message, "in", senderName, senderName, false, true, "");
      });

      connection.on("groupInviteResponseDeclined", function (group, message) {
        clearInvitedUsers();
        drawChatMessageWindow(group.name, group.groupId, message, "in", "Assistant", group.name, true, true, "");
        drawControlWindowChatGroup(group);
      });

      connection.on("groupInviteResponseAccepted", function (group, hostName, userName) {
        console.log('=== GROUP INVITE RESPONSE ACCEPTED EVENT RECEIVED ===');
        console.log('Group:', group);
        console.log('Host Name:', hostName);
        console.log('User Name:', userName);
        console.log('Current settings.userName:', settings.userName);
        console.log('Current settings.userNameId:', settings.userNameId);
        clearInvitedUsers();
        // check if the user is the one who has accepted the invite
        if (settings.userName === userName) {
          // this is the user who has accepted the invite so get the history
          console.log('Current user accepted invite, drawing group:', group.groupId);
          // this is a blank message (hack/workaround) to open the message window, this message will be cleared in the receiveGroupHistory
          drawChatMessageWindow(group.groupId, "Assistant", "Message from Assistant", "out", group.name, hostName, true, true, "", true);
          // get history
          safeInvoke("GetGroupHistory", settings.tenantId, group.groupId, 0);
          drawChatMessageWindowMenuBar(group.groupId, group.hostNameId);
        } else {
          // the user who has accepted the invite is not the current user so notify the current user a user has joined
          drawChatMessageWindow(group.name, group.groupId, userName + " has joined the group.", "in", "Assistant", group.name, true, true, "");
          
          // If this was a group where the current user was alone, add a message about no longer being alone
          if (group.users.filter(function(u) { return !u.leftGroup && u.userNameId !== settings.userNameId; }).length === 1) {
            drawChatMessageWindow("Assistant", group.groupId, "You are no longer alone in this group. Other users can now see your messages.", "in", "Assistant", group.name, true, true, "");
          }
        }
        // Always draw the control window group to ensure it's visible
        console.log('Drawing control window group:', group.name, 'with users:', group.users);
        drawControlWindowChatGroup(group);
        
        // Ensure the group is visible after drawing (failsafe for host offline scenarios)
        setTimeout(function() {
          var groupContainer = $(CSS.escape(group.name));
          if (groupContainer.length > 0) {
            groupContainer.show();
            console.log('Failsafe: Ensured group is visible:', group.name);
          }
        }, 100);
      });

      connection.on("drawControlWindowChatGroup", function (group) {
        clearInvitedUsers();
        drawControlWindowChatGroup(group);
      });

      connection.on("receiveGroupMessage", function (group, user, message) {
        var isHost = (settings.userNameId === group.hostNameId) ? true : false;
        // check if i am the sender
        if (settings.userNameId == user.nameId) {
          drawChatMessageWindow(group.groupId, group.groupId, message, "out", group.name, user.name, true, true, "", true);
        } else {
          drawChatMessageWindow(group.groupId, group.groupId, message, "in", user.name, group.name, true, true, "");
        }
        drawChatMessageWindowMenuBar(group.groupId, group.hostNameId);
      });

      connection.on("notifyUserLeftGroup", function (group, targetUserNameId, message) {
        // check the user leaving is not the current user
        if (targetUserNameId !== settings.userNameId) {
          // Enhanced message with history information
          var enhancedMessage = message;
          drawChatMessageWindow("Assistant", group.groupId, enhancedMessage, "in", "Assistant", group.name, true, true, "");
          
          // Remove the user from pending invite list if they were selected
          var userInvitedIndex = g_clientsPendingInvite.indexOf(targetUserNameId);
          if (userInvitedIndex !== -1) {
            g_clientsPendingInvite.splice(userInvitedIndex, 1);
          }
          
          drawControlWindowChatGroup(group);
          
          // Update the menu bar for the currently open group chat window
          var groupChatWindow = $('#group-' + group.groupId);
          if (groupChatWindow.length > 0) {
            console.log('Updating menu bar for group chat window after user left:', group.groupId, 'Host:', group.hostNameId);
            drawChatMessageWindowMenuBar(group.groupId, group.hostNameId);
          }
          
          // Update the invite menu bar to reflect the new count
          if (_isInviteMode) {
            drawChatMessageWindowMenuBar(g_inviteToGroupId, g_groupHostNameId);
          }
          
          // Check if user is now alone in group and add assistant message
          if (isUserAloneInGroup(group)) {
            drawChatMessageWindow("Assistant", group.groupId, "You are now the only active user in this group. Your messages will only be seen by other users when they join or return to this chat.", "in", "Assistant", group.name, true, true, "");
          }
        } else {
          // user leaving is the current user so remove the group
          closeMessageWindow($('#group-' + group.groupId));
          $('#' + group.groupId).remove();
          
          // Also remove the entire group container if it exists
          var groupContainer = $(CSS.escape(group.name));
          if (groupContainer.length > 0) {
            groupContainer.remove();
          }
        }
      });

      // called when a group has been deleted
      connection.on("deletedGroup", function (groupId) {
        closeMessageWindow($('#group-' + groupId));
        $('#' + groupId).remove();
      });

      // called when group deletion is denied (non-host attempting to delete)
      connection.on("deleteGroupDenied", function (message) {
        alert(message);
      });

      connection.on("receiveHistory", function (targetUserNameId, chatMessages, hasMore) {
      // first remove any existing messages for initial load
      let dataId = 'group-' + targetUserNameId;
      const contactContainer = document.querySelector(`div.contactContainer[data-id='${dataId}']`);
      if (contactContainer != null) {
        const conversationContainer = contactContainer.nextElementSibling;
        const firstOuterMessage = conversationContainer.querySelector('.outerMessage');
        if (firstOuterMessage) {
          firstOuterMessage.remove();
        }
        
        // Remove existing load more button if present
        const loadMoreBtn = conversationContainer.querySelector('.load-more-button');
        if (loadMoreBtn) {
          loadMoreBtn.remove();
        }
      }
      
      // Add load more button if there are more messages
      if (hasMore && contactContainer != null) {
        const conversationContainer = contactContainer.nextElementSibling;
        const loadMoreBtn = document.createElement('div');
        loadMoreBtn.className = 'load-more-button';
        loadMoreBtn.innerHTML = '<a href="#" class="link underline" style="font-size: 12px; padding: 10px; display: block; text-align: center;">📜 Show Earlier Messages</a>';
        loadMoreBtn.onclick = function(e) {
          e.preventDefault();
          loadMoreBtn.style.display = 'none';
          // Calculate current message count as skip value
          const messageCount = conversationContainer.querySelectorAll('.outerMessage').length;
          safeInvoke("GetHistory", settings.tenantId, targetUserNameId, settings.userNameId, messageCount);
        };
        conversationContainer.insertBefore(loadMoreBtn, conversationContainer.firstChild);
      }
      
      for (var i = 0; i < chatMessages.length; i++) {
        var message = chatMessages[i];
        var thisUserIsSender = false;
        if (settings.userNameId == message.senderUserNameId) {
          thisUserIsSender = true;
        }
        if (thisUserIsSender) {
          drawChatMessageWindow(message.targetUserNameId, message.senderUserNameId, message.message, "out", message.targetUserName, message.senderUserName, false, true, message.messageDate, true);
        } else {
          drawChatMessageWindow(message.targetUserNameId, message.senderUserNameId, message.message, "in", message.senderUserName, message.targetUserName, false, true, message.messageDate);
        }
      }
    });

      connection.on("receiveGroupHistory", function (chatMessages, group, hasMore) {
        // first remove any existing messages for initial load
        let dataId = 'group-' + group.groupId;
        const contactContainer = document.querySelector(`div.contactContainer[data-id='${dataId}']`);
        const conversationContainer = contactContainer.nextElementSibling;
        const outerMessages = conversationContainer.querySelectorAll('.outerMessage');
        // Loop through the NodeList and remove each element
        outerMessages.forEach(message => message.remove());
        
        // Remove existing load more button if present
        const loadMoreBtn = conversationContainer.querySelector('.load-more-button');
        if (loadMoreBtn) {
          loadMoreBtn.remove();
        }
        
        // Add load more button if there are more messages
        if (hasMore) {
          const loadMoreBtn = document.createElement('div');
          loadMoreBtn.className = 'load-more-button';
          loadMoreBtn.innerHTML = '<a href="#" class="link underline" style="font-size: 12px; padding: 10px; display: block; text-align: center;">📜 Show Earlier Messages</a>';
          loadMoreBtn.onclick = function(e) {
            e.preventDefault();
            loadMoreBtn.style.display = 'none';
            // Calculate current message count as skip value
            const messageCount = conversationContainer.querySelectorAll('.outerMessage').length;
            safeInvoke("GetGroupHistory", settings.tenantId, group.groupId, messageCount);
          };
          conversationContainer.insertBefore(loadMoreBtn, conversationContainer.firstChild);
        }
        
        // draw the message window for each message
        for (var i = 0; i < chatMessages.length; i++) {
          var message = chatMessages[i];
          var thisUserIsSender = false;
          if (settings.userNameId == message.senderUserNameId) {
            thisUserIsSender = true;
          }
          if (thisUserIsSender) {
            drawChatMessageWindow(group.groupId, group.groupId, message.message, "out", group.name, message.senderUserName, true, true, message.messageDate, false);
          } else {
            drawChatMessageWindow(group.groupId, group.groupId, message.message, "in", message.senderUserName, group.name, true, true, message.messageDate);
          }
        }

        // Check if user is alone in group and add assistant message
        if (isUserAloneInGroup(group)) {
          drawChatMessageWindow("Assistant", group.groupId, "You are currently the only active user in this group. Your messages will only be seen by other users when they join or return to this chat.", "in", "Assistant", group.name, true, true, "");
        }

        // draw the message window menu bar
        var isHost = (settings.userNameId === group.hostNameId) ? true : false;
        // drawChatMessageWindowMenuBar(group.groupId, isHost);
        drawChatMessageWindowMenuBar(group.groupId, group.hostNameId);
      });

      // Handle loading additional one-to-one messages
      connection.on("receiveMoreHistory", function (targetUserNameId, chatMessages, hasMore) {
        let dataId = 'group-' + targetUserNameId;
        const contactContainer = document.querySelector(`div.contactContainer[data-id='${dataId}']`);
        if (contactContainer != null) {
          const conversationContainer = contactContainer.nextElementSibling;
          
          // Remove existing load more button
          const loadMoreBtn = conversationContainer.querySelector('.load-more-button');
          if (loadMoreBtn) {
            loadMoreBtn.remove();
          }
          
          // Add new load more button if there are more messages
          if (hasMore) {
            const newLoadMoreBtn = document.createElement('div');
            newLoadMoreBtn.className = 'load-more-button';
            newLoadMoreBtn.innerHTML = '<a href="#" class="link underline" style="font-size: 12px; padding: 10px; display: block; text-align: center;">📜 Show Earlier Messages</a>';
            newLoadMoreBtn.onclick = function(e) {
              e.preventDefault();
              newLoadMoreBtn.style.display = 'none';
              const messageCount = conversationContainer.querySelectorAll('.outerMessage').length;
              safeInvoke("GetHistory", settings.tenantId, targetUserNameId, settings.userNameId, messageCount);
            };
            conversationContainer.insertBefore(newLoadMoreBtn, conversationContainer.firstChild);
          }
          
          // Add new messages at the beginning
          for (var i = 0; i < chatMessages.length; i++) {
            var message = chatMessages[i];
            var thisUserIsSender = settings.userNameId == message.senderUserNameId;
            
            if (thisUserIsSender) {
              drawChatMessageWindow(message.targetUserNameId, message.senderUserNameId, message.message, "out", message.targetUserName, message.senderUserName, false, true, message.messageDate, true, true);
            } else {
              drawChatMessageWindow(message.targetUserNameId, message.senderUserNameId, message.message, "in", message.senderUserName, message.targetUserName, false, true, message.messageDate, false, true);
            }
          }
          
          // Add "show newer" button since we're loading earlier messages
          const showNewerBtn = document.createElement('div');
          showNewerBtn.className = 'show-newer-button';
          showNewerBtn.innerHTML = '<a href="#" class="link underline" style="font-size: 12px; padding: 10px; display: block; text-align: center;">📥 Show Newer Messages</a>';
          showNewerBtn.onclick = function(e) {
            e.preventDefault();
            showNewerBtn.style.display = 'none';
            // Get the newest message date
            const messages = conversationContainer.querySelectorAll('.outerMessage');
            const newestMessage = messages[messages.length - 1];
            const newestDate = newestMessage.getAttribute('data-message-date');
            if (newestDate) {
              safeInvoke("GetNewerHistory", settings.tenantId, targetUserNameId, settings.userNameId, newestDate);
            }
          };
          conversationContainer.appendChild(showNewerBtn);
        }
      });

      // Handle loading additional group messages
      connection.on("receiveMoreGroupHistory", function (chatMessages, group, hasMore) {
        let dataId = 'group-' + group.groupId;
        const contactContainer = document.querySelector(`div.contactContainer[data-id='${dataId}']`);
        if (contactContainer != null) {
          const conversationContainer = contactContainer.nextElementSibling;
          
          // Remove existing load more button
          const loadMoreBtn = conversationContainer.querySelector('.load-more-button');
          if (loadMoreBtn) {
            loadMoreBtn.remove();
          }
          
          // Add new load more button if there are more messages
          if (hasMore) {
            const newLoadMoreBtn = document.createElement('div');
            newLoadMoreBtn.className = 'load-more-button';
            newLoadMoreBtn.innerHTML = '<a href="#" class="link underline" style="font-size: 12px; padding: 10px; display: block; text-align: center;">📜 Show Earlier Messages</a>';
            newLoadMoreBtn.onclick = function(e) {
              e.preventDefault();
              newLoadMoreBtn.style.display = 'none';
              const messageCount = conversationContainer.querySelectorAll('.outerMessage').length;
              safeInvoke("GetGroupHistory", settings.tenantId, group.groupId, messageCount);
            };
            conversationContainer.insertBefore(newLoadMoreBtn, conversationContainer.firstChild);
          }
          
          // Add new messages at the beginning
          for (var i = 0; i < chatMessages.length; i++) {
            var message = chatMessages[i];
            var thisUserIsSender = settings.userNameId == message.senderUserNameId;
            
            if (thisUserIsSender) {
              drawChatMessageWindow(group.groupId, group.groupId, message.message, "out", group.name, message.senderUserName, true, true, message.messageDate, false, true);
            } else {
              drawChatMessageWindow(group.groupId, group.groupId, message.message, "in", message.senderUserName, group.name, true, true, message.messageDate, false, true);
            }
          }
          
          // Add "show newer" button since we're loading earlier messages
          const showNewerBtn = document.createElement('div');
          showNewerBtn.className = 'show-newer-button';
          showNewerBtn.innerHTML = '<a href="#" class="link underline" style="font-size: 12px; padding: 10px; display: block; text-align: center;">📥 Show Newer Messages</a>';
          showNewerBtn.onclick = function(e) {
            e.preventDefault();
            showNewerBtn.style.display = 'none';
            // Get the newest message date
            const messages = conversationContainer.querySelectorAll('.outerMessage');
            const newestMessage = messages[messages.length - 1];
            const newestDate = newestMessage.getAttribute('data-message-date');
            if (newestDate) {
              safeInvoke("GetNewerGroupHistory", settings.tenantId, group.groupId, newestDate);
            }
          };
          conversationContainer.appendChild(showNewerBtn);
        }
      });

      // Handle loading newer one-to-one messages
      connection.on("receiveNewerHistory", function (targetUserNameId, chatMessages, hasMore) {
        let dataId = 'group-' + targetUserNameId;
        const contactContainer = document.querySelector(`div.contactContainer[data-id='${dataId}']`);
        if (contactContainer != null) {
          const conversationContainer = contactContainer.nextElementSibling;
          
          // Remove existing "show newer" button
          const showNewerBtn = conversationContainer.querySelector('.show-newer-button');
          if (showNewerBtn) {
            showNewerBtn.remove();
          }
          
          // Add new messages at the end
          for (var i = 0; i < chatMessages.length; i++) {
            var message = chatMessages[i];
            var thisUserIsSender = settings.userNameId == message.senderUserNameId;
            
            if (thisUserIsSender) {
              drawChatMessageWindow(message.targetUserNameId, message.senderUserNameId, message.message, "out", message.targetUserName, message.senderUserName, false, true, message.messageDate, true);
            } else {
              drawChatMessageWindow(message.targetUserNameId, message.senderUserNameId, message.message, "in", message.senderUserName, message.targetUserName, false, true, message.messageDate);
            }
          }
          
          // Add "show newer" button if there are more messages
          if (hasMore) {
            const showNewerBtn = document.createElement('div');
            showNewerBtn.className = 'show-newer-button';
            showNewerBtn.innerHTML = '<a href="#" class="link underline" style="font-size: 12px; padding: 10px; display: block; text-align: center;">📥 Show Newer Messages</a>';
            showNewerBtn.onclick = function(e) {
              e.preventDefault();
              showNewerBtn.style.display = 'none';
              // Get the newest message date
              const messages = conversationContainer.querySelectorAll('.outerMessage');
              const newestMessage = messages[messages.length - 1];
              const newestDate = newestMessage.getAttribute('data-message-date');
              if (newestDate) {
                safeInvoke("GetNewerHistory", settings.tenantId, targetUserNameId, settings.userNameId, newestDate);
              }
            };
            conversationContainer.appendChild(showNewerBtn);
          }
        }
      });

      // Handle loading newer group messages
      connection.on("receiveNewerGroupHistory", function (chatMessages, group, hasMore) {
        let dataId = 'group-' + group.groupId;
        const contactContainer = document.querySelector(`div.contactContainer[data-id='${dataId}']`);
        if (contactContainer != null) {
          const conversationContainer = contactContainer.nextElementSibling;
          
          // Remove existing "show newer" button
          const showNewerBtn = conversationContainer.querySelector('.show-newer-button');
          if (showNewerBtn) {
            showNewerBtn.remove();
          }
          
          // Add new messages at the end
          for (var i = 0; i < chatMessages.length; i++) {
            var message = chatMessages[i];
            var thisUserIsSender = settings.userNameId == message.senderUserNameId;
            
            if (thisUserIsSender) {
              drawChatMessageWindow(group.groupId, group.groupId, message.message, "out", group.name, message.senderUserName, true, true, message.messageDate, false);
            } else {
              drawChatMessageWindow(group.groupId, group.groupId, message.message, "in", message.senderUserName, group.name, true, true, message.messageDate);
            }
          }
          
          // Add "show newer" button if there are more messages
          if (hasMore) {
            const showNewerBtn = document.createElement('div');
            showNewerBtn.className = 'show-newer-button';
            showNewerBtn.innerHTML = '<a href="#" class="link underline" style="font-size: 12px; padding: 10px; display: block; text-align: center;">📥 Show Newer Messages</a>';
            showNewerBtn.onclick = function(e) {
              e.preventDefault();
              showNewerBtn.style.display = 'none';
              // Get the newest message date
              const messages = conversationContainer.querySelectorAll('.outerMessage');
              const newestMessage = messages[messages.length - 1];
              const newestDate = newestMessage.getAttribute('data-message-date');
              if (newestDate) {
                safeInvoke("GetNewerGroupHistory", settings.tenantId, group.groupId, newestDate);
              }
            };
            conversationContainer.appendChild(showNewerBtn);
          }
        }
      });

      // Handle user disconnection from group chats
      connection.on("userDisconnectedFromGroup", function (groupId, disconnectedUserNameId, disconnectedUserName, message) {
        if (!isActive()) return;
        
        // Check if there's an open group chat window
        var groupChatWindowId = "#group-" + groupId;
        if ($(groupChatWindowId).length > 0) {
          // Display the disconnection message in the group chat window
          var currentTime = new Date();
            drawMessage($(groupChatWindowId).find(".conversationContainer").eq(0), message, "in", "Assistant", currentTime);
        }
      });

      // Handle user reconnection to group chats
      connection.on("userReconnectedToGroup", function (groupId, reconnectedUserNameId, reconnectedUserName, message) {
        if (!isActive()) return;
        
        // Check if there's an open group chat window
        var groupChatWindowId = "#group-" + groupId;
        if ($(groupChatWindowId).length > 0) {
          // Display the reconnection message in the group chat window
          var currentTime = new Date();
            drawMessage($(groupChatWindowId).find(".conversationContainer").eq(0), message, "in", "Assistant", currentTime);
        }
      });
    }

    // Add avatar upload popup HTML to the page only if feature is enabled
    checkAvatarUploadFeature().then(function(isEnabled) {
      if (isEnabled && $("#avatarUploadPopup").length === 0) {
        var avatarUploadHTML = `
          <div id="avatarUploadOverlay"></div>
          <div id="avatarUploadPopup">
            <h3>Upload Avatar Image</h3>
            <div class="upload-section">
              <div class="file-input-container">
                <input type="file" id="avatarFileInput" accept="image/*" />
                <div class="error-message" id="avatarErrorMessage"></div>
                <div class="success-message" id="avatarSuccessMessage"></div>
              </div>
              <div class="preview-container">
                <img id="avatarPreview" />
              </div>
              <div class="upload-progress">
                <div class="progress-bar">
                  <div class="progress-fill" id="avatarProgressFill"></div>
                </div>
              </div>
            </div>
            <div class="button-container">
              <button class="btn btn-primary" id="avatarUploadBtn" disabled>Upload</button>
              <button class="btn btn-secondary" id="avatarCancelBtn">Cancel</button>
            </div>
          </div>
        `;
        $("body").append(avatarUploadHTML);
        
        // Setup avatar upload event handlers
        setupAvatarUploadHandlers();
      }
    });

    // Cache for features response
    var cachedFeatures = null;
    var featuresPromise = null;
    
    // Function to get features from cache or API
    function getFeatures() {
      return new Promise(function(resolve) {
        if (cachedFeatures) {
          resolve(cachedFeatures);
          return;
        }
        
        // If there's already a request in progress, wait for it
        if (featuresPromise) {
          featuresPromise.then(resolve);
          return;
        }
        
        featuresPromise = $.ajax({
          url: settings.hubServer + "features",
          type: "GET",
          success: function(response) {
            console.log("Feature response:", response);
            cachedFeatures = response;
            featuresPromise = null;
            resolve(response);
          },
          error: function() {
            // If feature check fails, default to disabled
            var defaultFeatures = { AvatarUpload: false, Version: "Not set" };
            cachedFeatures = defaultFeatures;
            featuresPromise = null;
            resolve(defaultFeatures);
          }
        });
      });
    }
    
    // Function to check if avatar upload feature is enabled
    function checkAvatarUploadFeature() {
      return getFeatures().then(function(features) {
        return features.avatarUpload || features.AvatarUpload || false;
      });
    }

    // Avatar upload functions
    function showAvatarUploadPopup() {
      $("#avatarUploadOverlay").show();
      $("#avatarUploadPopup").show();
      $("#avatarFileInput").val("");
      $("#avatarPreview").hide();
      $("#avatarUploadBtn").prop("disabled", true);
      $("#avatarErrorMessage").hide();
      $("#avatarSuccessMessage").hide();
      $(".upload-progress").hide();
    }

    function hideAvatarUploadPopup() {
      $("#avatarUploadOverlay").hide();
      $("#avatarUploadPopup").hide();
    }

    function setupAvatarUploadHandlers() {
      // File input change handler
      $("#avatarFileInput").change(function(e) {
        var file = e.target.files[0];
        if (file) {
          // Validate file type
          var allowedTypes = ["image/jpeg", "image/jpg", "image/png", "image/gif"];
          var fileType = file.type;
          
          if (!allowedTypes.includes(fileType)) {
            showAvatarError("Please select a valid image file (JPEG, PNG, or GIF)");
            return;
          }

          // Validate file size (5MB limit)
          if (file.size > 5 * 1024 * 1024) {
            showAvatarError("File size must be less than 5MB");
            return;
          }

          // Show preview
          var reader = new FileReader();
          reader.onload = function(e) {
            $("#avatarPreview").attr("src", e.target.result).show();
            $("#avatarUploadBtn").prop("disabled", false);
            $("#avatarErrorMessage").hide();
          };
          reader.readAsDataURL(file);
        }
      });

      // Upload button handler
      $("#avatarUploadBtn").click(function() {
        uploadAvatar();
      });

      // Cancel button handler
      $("#avatarCancelBtn").click(function() {
        hideAvatarUploadPopup();
      });

      // Overlay click handler
      $("#avatarUploadOverlay").click(function() {
        hideAvatarUploadPopup();
      });
    }

    function showAvatarError(message) {
      $("#avatarErrorMessage").text(message).show();
      $("#avatarSuccessMessage").hide();
      $("#avatarPreview").hide();
      $("#avatarUploadBtn").prop("disabled", true);
    }

    function showAvatarSuccess(message) {
      $("#avatarSuccessMessage").text(message).show();
      $("#avatarErrorMessage").hide();
    }

    function uploadAvatar() {
      var fileInput = $("#avatarFileInput")[0];
      var file = fileInput.files[0];
      
      if (!file) {
        showAvatarError("Please select a file");
        return;
      }

      // Show progress
      $(".upload-progress").show();
      $("#avatarUploadBtn").prop("disabled", true);
      $("#avatarCancelBtn").prop("disabled", true);

      var formData = new FormData();
      formData.append("file", file);
      formData.append("userNameId", settings.userNameId);
      formData.append("tenantId", settings.tenantId);

      // Upload the file
      $.ajax({
        url: settings.hubServer + "upload-avatar",
        type: "POST",
        data: formData,
        processData: false,
        contentType: false,
        xhr: function() {
          var xhr = new window.XMLHttpRequest();
          xhr.upload.addEventListener("progress", function(e) {
            if (e.lengthComputable) {
              var percentComplete = (e.loaded / e.total) * 100;
              $("#avatarProgressFill").css("width", percentComplete + "%");
            }
          });
          return xhr;
        },
        success: function(response) {
          showAvatarSuccess("Avatar uploaded successfully!");
          
          // Update the avatar image
          setTimeout(function() {
            var avatarUrl = settings.hubServer + "avatar/" + settings.tenantId + "/" + settings.userNameId + "?" + new Date().getTime();
            $("#imgAvatar").attr("src", avatarUrl);
            
            // Update all user avatars in the UI
            $(".user img").each(function() {
              if ($(this).closest(".user").attr("id") === settings.userNameId) {
                $(this).attr("src", avatarUrl);
              }
            });
            
            // Update contact header avatars
            $(".contactHeader img").each(function() {
              var contactContainer = $(this).closest(".contactContainer");
              if (contactContainer.data("userid") === settings.userNameId) {
                $(this).attr("src", avatarUrl);
              }
            });
            
            setTimeout(function() {
              hideAvatarUploadPopup();
            }, 1500);
          }, 500);
        },
        error: function(xhr, status, error) {
          var errorMessage = "Upload failed";
          if (xhr.responseText) {
            try {
              var response = JSON.parse(xhr.responseText);
              errorMessage = response.message || errorMessage;
            } catch (e) {
              errorMessage = xhr.responseText;
            }
          }
          showAvatarError(errorMessage);
        },
        complete: function() {
          $("#avatarUploadBtn").prop("disabled", false);
          $("#avatarCancelBtn").prop("disabled", false);
          $(".upload-progress").hide();
          $("#avatarProgressFill").css("width", "0%");
        }
      });
    }
    
    return this;
  };

}(jQuery));